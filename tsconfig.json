{
  "compilerOptions": {
    // 编译出JS的目标ES版本
    "target": "esnext",
    // 使用的ES版本
    "module": "esnext",
    "allowJs": false,
    // 用于选择模块解析策略，有'node'和'classic'两种类型
    "moduleResolution": "node",
    // 开启严格模式
    "strict": true,
    // 强制代码中使用的模块文件名必须和文件系统中的文件名保持大小写一致
    "forceConsistentCasingInFileNames": true,
    // 允许使用 xxx 代替 * as xxx 导入
    "allowSyntheticDefaultImports": true,
    // 指定 jsx 代码的生成: 'preserve', 'react-native', or 'react'
    "jsx": "preserve",
    // 用来指定编译时是否生成.map文件
    "sourceMap": true,
    // 通过为导入内容创建命名空间，实现CommonJS和ES模块之间的互操作性
    "esModuleInterop": true,
    // 是否可以导入 json module
    "resolveJsonModule": true,
    // 是否检测定义了但是没使用的变量
    "noUnusedLocals": true,
    // 是否检查是否有在函数体中没有使用的参数
    "noUnusedParameters": true,
    // 是否启用实验性的装饰器特性
    "experimentalDecorators": true,
    // ts中可以使用的库 这里配置为 dom 与 es模块
    "lib": [
      "dom",
      "esnext"
    ],
    // 不允许变量或函数参数具有隐式any类型
    "noImplicitAny": false,
    // 启用阻止对下载库的类型检查
    "skipLibCheck": true,
    // types用来指定需要包含的模块
    "types": [
      "vite/client",
      "element-plus/global"
    ],
    // 编译的时候删除注释
    "removeComments": true,
    // 使用绝对路径时使用baseUrl去解析导入路径
    "baseUrl": ".",
    // 为导入路径配置别名
    "paths": {
      "@/*": [
        "src/*"
      ],
      "#/*": [
        "types/*"
      ]
    },
    // 配置虚拟目录
    "rootDirs": []
  },
  // 指定需要编译文件
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts",
    "types/**/*.ts",
    "build/**/*.ts",
    "build/**/*.d.ts",
    "mock/**/*.ts",
    "vite.config.ts"
  ],
  // 指定不需要编译的文件
  "exclude": [
    "node_modules",
    "dist",
    "**/*.js"
  ]
}