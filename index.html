<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <!-- 基本favicon配置 -->
    <link rel="icon" href="/favicon.ico" />
    <!-- 支持不同设备和尺寸的favicon示例 -->
    <!-- 
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/favicon-16x16.png">
    <link rel="manifest" href="/icons/site.webmanifest">
    -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>吕相赫的前端世界</title>
</head>
<body>
<!-- 令 id="app" 便于vue进行挂载 -->
<div id="app"></div>
<!-- 引入main.ts文件 -->
<script type="module" src="/src/main.ts"></script>
</body>
</html>