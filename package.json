{"name": "front-end-scaffolding", "version": "1.0.0", "description": "吕相赫的企业级中台系统，前端技术模版", "type": "module", "main": "index.js", "scripts": {"dev": "vite --mode development", "build": "vite build", "lint:eslint": "eslint --cache --max-warnings 0 {src,mock}/**/*.{vue,ts,tsx} --fix", "lint:prettier": "prettier --write **/*.{js,json,tsx,css,less,scss,vue,html,md}", "prepare": "husky install", "lint:lint-staged": "lint-staged"}, "author": "吕相赫", "license": "ISC", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@excalidraw/excalidraw": "^0.18.0", "@types/three": "^0.174.0", "axios": "^1.6.7", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "echarts": "^5.6.0", "element-plus": "^2.6.0", "gridstack": "^11.5.1", "gsap": "^3.12.7", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "lunar-javascript": "^1.7.2", "marked": "^15.0.6", "nprogress": "^0.2.0", "particles.js": "^2.0.0", "pdfjs-dist": "^5.1.91", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "sortablejs": "^1.15.6", "swiper": "^11.2.2", "three": "^0.174.0", "vue": "^3.4.21", "vue-router": "^4.3.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@eslint/create-config": "^1.4.0", "@eslint/js": "^9.16.0", "@types/node": "^22.10.1", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-vue": "^9.32.0", "globals": "^15.13.0", "husky": "^9.1.7", "less": "^4.2.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "postcss-html": "^1.7.0", "postcss-less": "^6.0.0", "prettier": "^3.4.2", "stylelint": "^16.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-vue": "^1.0.0", "stylelint-less": "^3.0.1", "stylelint-order": "^6.0.4", "typescript": "^5.3.3", "typescript-eslint": "^8.17.0", "unplugin-auto-import": "^19.0.0", "unplugin-element-plus": "^0.9.0", "unplugin-icons": "^0.17.4", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.2", "vite-plugin-mock": "^3.0.2", "vue-tsc": "^2.1.10"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}}